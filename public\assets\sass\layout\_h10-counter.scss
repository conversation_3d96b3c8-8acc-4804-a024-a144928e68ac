@use "../utilities" as *;

/* START: Counter CSS */
.h10-counter-section {
	background-color: var(--tj-color-theme-dark);
	overflow: hidden;
	padding: 60px 0;

	@media #{$lg} {
		padding: 50px 0;
	}
	@media #{$md, $sm, $xs} {
		padding: 40px 0;
	}

	.container-fluid {
		max-width: 1920px;
		margin: 0 auto;
		padding: 0 15px;

		@media #{$lg} {
			padding: 0 30px;
		}
		@media #{$md, $sm, $xs} {
			padding: 0 20px;
		}
	}

	.counter-wrapper {
		background-color: var(--tj-color-theme-dark);
		margin: 0 -15px;

		@media #{$sm} {
			max-width: 100%;
			margin: 0 -10px;
		}
		@media #{$xs} {
			margin: 0 -5px;
		}
	}
}

.counter-item {
	&.style-2 {
		flex: 0 0 auto;
		border-color: var(--tj-color-border-1);
		padding: 54px 15px 60px 15px;
		margin-bottom: 30px;

		// Desktop - 6 items per row
		@media (min-width: 1200px) {
			max-width: 16.666667%; // 100% / 6
			flex: 0 0 16.666667%;
		}

		// Large tablets - 3 items per row
		@media (min-width: 992px) and (max-width: 1199px) {
			max-width: 33.333333%; // 100% / 3
			flex: 0 0 33.333333%;
		}

		// Medium tablets - 2 items per row
		@media (min-width: 768px) and (max-width: 991px) {
			max-width: 50%;
			flex: 0 0 50%;
		}

		// Small tablets - 2 items per row
		@media (min-width: 576px) and (max-width: 767px) {
			max-width: 50%;
			flex: 0 0 50%;
		}

		// Mobile - 1 item per row
		@media (max-width: 575px) {
			max-width: 100%;
			flex: 0 0 100%;
			margin-bottom: 20px;
		}

		.counter-item-inner {
			max-width: 100%;
			margin: 0 auto;
			padding: 0 10px;

			@media #{$lg} {
				max-width: 100%;
			}
			@media #{$md, $sm, $xs} {
				max-width: 100%;
				padding: 0 5px;
			}
		}

		.counter-label {
			font-size: 14px;
			font-weight: 600;
			color: var(--tj-color-common-white-2);
			text-transform: uppercase;
			letter-spacing: 0.5px;
		}

		&::before {
			border: 3px solid var(--tj-color-theme-dark);
			background-color: var(--tj-color-theme-primary);
		}
		&::after {
			background-color: var(--tj-color-theme-primary);
			@media #{$md, $sm, $xs} {
				width: 16px;
				height: 16px;
				inset-inline-end: -8px;
			}
		}
		.number {
			color: var(--tj-color-common-white);
			font-size: 48px;
			font-weight: var(--tj-fw-bold);
			line-height: 1.2;
			margin-bottom: 8px;

			@media #{$xl} {
				font-size: 42px;
			}
			@media #{$lg} {
				font-size: 36px;
			}
			@media #{$md} {
				font-size: 32px;
			}
			@media #{$sm} {
				font-size: 28px;
			}
			@media #{$xs} {
				font-size: 24px;
			}
		}
		.sub-title {
			color: var(--tj-color-common-white-2);
			font-size: 14px;
			line-height: 1.4;
			display: block;

			@media #{$lg} {
				font-size: 13px;
			}
			@media #{$md, $sm, $xs} {
				font-size: 12px;
			}
		}

		// Legacy responsive styles for compatibility
		@media #{$xl} {
			padding: 50px 15px 60px 15px;
		}
		@media #{$lg} {
			padding: 40px 15px 50px 15px;
		}
		@media #{$md} {
			padding: 35px 15px 40px 15px;
		}
		@media #{$sm} {
			max-width: 50%;
			padding: 30px 20px 35px;
		}
		@media #{$xs} {
			max-width: 50%;
			padding: 30px 15px;
		}
	}
}
/* !END: Counter CSS */
